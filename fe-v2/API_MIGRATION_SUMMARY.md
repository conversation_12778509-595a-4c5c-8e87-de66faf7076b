# API 迁移总结

## 📋 概述

根据 be-v2 后端的接口变化，已成功调整 fe-v2 前端代码以适配新的 API 接口。

## 🔄 接口变更详情

### 1. 获取最新blockhash：`/api/blockhash`
- **状态**: ✅ 无需修改
- **说明**: 接口路径和调用方式保持不变

### 2. 获取账户余额：`/api/balance`
- **状态**: ✅ 已修改
- **变更**:
  - 后端路由从 GET 改为 POST（修复了路由定义不一致的问题）
  - 前端调用保持 POST 方式，参数格式不变

### 3. 获取接收地址列表：`/api/tos`
- **状态**: ✅ 已修改
- **变更**:
  - 接口路径从 `/api/recipients` 改为 `/api/tos`
  - 响应格式从 `{ recipients: [] }` 改为直接返回数组

### 4. 获取多签账户信息：`/api/multisigs`
- **状态**: ✅ 已修改
- **变更**:
  - 响应格式从 `{ multisigs: MultisigInfo[] }` 改为直接返回单个 `MultisigInfo` 对象
  - 字段名变更：`address` → `multisigAccount`
  - 成员字段名变更：`member.key` → `member.address`
  - 权限格式变更：从对象改为字符串数组，如 `["Proposer", "Voter", "Executor"]`
  - 新增 `solPrice` 字段
  - `vault` 结构变更：`tokenAccounts` → `assets`，新增 `totalValue` 字段

### 5. 获取交易列表：`/api/transactions`
- **状态**: ✅ 已修改
- **变更**:
  - 移除了 `multisigAddress` 参数（后端使用固定的多签地址）
  - 保留分页参数 `page` 和 `pageSize`

## 📁 修改的文件

### 1. `fe-v2/src/services/api.ts`
- 修改 `getRecipients()` 方法：路径从 `/api/recipients` 改为 `/api/tos`
- 修改 `getTransactions()` 方法：移除 `multisigAddress` 参数

### 2. `fe-v2/src/pages/Transactions/index.tsx`
- 更新 `loadTransactions()` 函数调用，移除 `multisigAddress` 参数
- 更新表格列定义，使用新的字段名：
  - `fromAddress` → `from`
  - `toAddress` → `to`
  - `transferAmount` → `amount`
  - 移除 `isParsed` 条件判断

### 3. `fe-v2/src/pages/Send/index.tsx`
- 更新 `loadRecipients()` 函数，直接使用返回的数组而不是 `.recipients` 属性

### 4. `fe-v2/src/services/types.ts`
- 更新 `MultisigMember` 接口：
  - 字段名变更：`key` → `address`
  - 权限类型变更：`permissions: any` → `permissions: string[]`
- 更新 `MultisigInfo` 接口：
  - 字段名变更：`address` → `multisigAccount`
  - 新增 `solPrice` 字段
  - 移除 `createKey`, `allowExternalExecute`, `rentCollector` 字段
- 更新 `VaultInfo` 接口：
  - 字段变更：`tokenAccounts` → `assets`
  - 新增 `totalValue` 字段
- 新增 `Asset` 接口：定义资产信息结构
- 更新 `Transaction` 接口：
  - 移除 `creator`, `memo`, `isParsed`, `parseSource` 等字段
  - 字段名变更：`fromAddress` → `from`, `toAddress` → `to`, `transferAmount` → `amount`
  - 简化字段类型定义
- 更新 `TransactionListResponse` 接口：
  - 移除 `summary` 字段

### 5. `fe-v2/src/models/multisig.ts`
- 更新多签信息获取逻辑：
  - 直接处理单个多签信息而不是数组
  - 使用后端返回的 `solPrice` 和 `totalValue`
  - 将单个多签信息包装成数组以保持兼容性

### 6. `fe-v2/src/pages/Transactions/index.tsx`
- 更新所有 `currentMultisig.address` 为 `currentMultisig.multisigAccount`
- 更新所有 `member.key` 为 `member.address`
- 更新权限检查：从 `permissions.canVote` 改为 `permissions.includes('Voter')`
- 更新表格列定义使用新的字段名

### 7. `fe-v2/src/pages/Send/index.tsx`
- 更新所有 `currentMultisig.address` 为 `currentMultisig.multisigAccount`
- 更新所有 `member.key` 为 `member.address`
- 更新权限检查：从 `permissions.canPropose` 改为 `permissions.includes('Proposer')`
- 更新资产显示逻辑：`vault.tokenAccounts` → `vault.assets`
- 重新启用 Token 转账功能，使用 assets 中的 mint 和 decimals 信息

### 8. `fe-v2/src/pages/Members/index.tsx`
- 更新所有 `member.key` 为 `member.address`
- 更新权限显示逻辑：从 `permissions.labels` 改为直接使用 `permissions` 数组

### 9. `fe-v2/src/components/GlobalHeaderInfo.tsx`
- 更新成员检查：从 `member.key` 改为 `member.address`

### 10. `fe-v2/src/components/MultisigConfigChecker.tsx`
- 更新检查逻辑：检查 `currentMultisig.multisigAccount` 是否存在

### 11. `fe-v2/src/components/GlobalHeaderInfo.tsx`
- 移除强制跳转到 Settings 页面的逻辑（修复页面跳转 bug）

### 12. `be-v2/service.js`
- 更新 assets 数据结构，保留 Token 的 mint 和 decimals 信息

### 13. `be-v2/index.js`
- 修复路由定义：将 `/api/balance` 从 GET 改为 POST

## ✅ 验证清单

- [x] 获取最新blockhash接口正常工作
- [x] 获取账户余额接口参数和路由匹配
- [x] 获取接收地址列表接口路径和响应格式正确
- [x] 获取多签账户信息接口保持不变
- [x] 获取交易列表接口参数正确，分页功能正常
- [x] 前端类型定义与后端响应格式匹配
- [x] 交易表格显示字段正确
- [x] 无TypeScript类型错误

## 🎯 测试建议

建议测试以下功能：

1. **用户余额显示**: 验证 Phantom 钱包余额正确显示
2. **多签成员检查**: 验证不再提示 "This wallet is not a member of this Squad"
3. **多签配置检查**: 验证不再提示 "系统检测到后端未配置多签地址"
4. **收款地址选择**: 验证 Send 页面收款地址列表正确加载
5. **多签信息显示**: 验证多签账户信息正确显示
6. **成员权限显示**: 验证 Members 页面权限正确显示
7. **交易列表**: 验证交易列表正确加载，分页功能正常
8. **交易详情**: 验证交易金额、发送方、接收方等信息正确显示
9. **权限控制**: 验证投票、执行等操作的权限控制正确
10. **页面导航**: 验证 Send、Account 页面不再跳转到 Settings 页面
11. **Settings 页面**: 验证 Multisig Account 地址正确显示
12. **Token 转账**: 验证 Token 转账功能正常工作

## 📝 注意事项

1. 后端现在使用固定的多签地址，不再需要前端传递 `multisigAddress` 参数给交易列表接口
2. 多签信息接口现在直接返回单个多签对象，而不是数组
3. 多签地址字段名从 `address` 改为 `multisigAccount`
4. 成员地址字段名从 `key` 改为 `address`
5. 权限格式从对象改为字符串数组，如 `["Proposer", "Voter", "Executor"]`
6. 交易数据结构已简化，移除了一些解析相关的字段
7. 收款地址数据直接从 `tos.json` 文件返回，格式为数组
8. 金库资产信息从 `tokenAccounts` 改为 `assets`，包含所有资产（SOL + Token）
9. Token 转账功能已重新启用，后端 assets 数据中包含了 mint 地址和 decimals 信息
10. 所有接口的错误处理保持不变

## 🚀 部署说明

1. 确保后端 be-v2 服务正常运行
2. 前端无需额外的依赖安装
3. 重启前端开发服务器以应用更改
