import React, { useEffect, useState } from 'react';
import { Spin, Button, Space, message } from 'antd';
import { SyncOutlined, DollarOutlined } from '@ant-design/icons';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { useModel, history } from '@umijs/max';
import { formatUsdAmount } from '@/utils/price';

const GlobalHeaderInfo: React.FC = () => {
  const { connected, publicKey } = useWallet();
  const { userBalance, totalVaultValue, loading, currentMultisig, refreshMultisigs } = useModel('multisig');
  const [refreshing, setRefreshing] = useState(false);
  const [memberCheckShown, setMemberCheckShown] = useState(false);

  // 组件挂载时加载数据（应用初始化，只执行一次）
  useEffect(() => {
    refreshMultisigs(false, 'init');
  }, []);

  // 移除强制跳转逻辑 - 这会导致所有页面都跳转到 Settings
  // useEffect(() => {
  //   if (!loading) {
  //     // 如果没有多签地址且当前不在 Settings 页面，则跳转到 Settings 页面
  //     if (history.location.pathname !== '/settings') {
  //       history.push('/settings');
  //     }
  //   }
  // }, [loading]);

  // 检查用户是否为多签成员（简化为消息提示）
  useEffect(() => {
    if (!loading && connected && publicKey && currentMultisig && !memberCheckShown) {
      const userPublicKeyString = publicKey.toBase58();
      const isMember = currentMultisig.members.some(member => member.address === userPublicKeyString);

      if (!isMember) {
        message.warning({
          content: 'This wallet is not a member of this Squad.',
          duration: 8,
          key: 'member-check'
        });
        setMemberCheckShown(true);
      }
    }
  }, [loading, connected, publicKey, currentMultisig, memberCheckShown]);

  // 重置成员检查状态（当钱包或多签变化时）
  useEffect(() => {
    setMemberCheckShown(false);
  }, [publicKey, currentMultisig]);

  // 处理刷新按钮点击
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshMultisigs(true); // 强制刷新
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <div style={{
      marginBottom: 16,
      padding: '8px',
      background: '#fff',
      borderRadius: '6px',
      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        fontWeight: 500,
        color: '#666'
      }}>
        <Space>
          <Button
            type="primary"
            size="small"
            loading={refreshing}
            onClick={handleRefresh}
            title="刷新数据"
          >
            刷新
          </Button>
          <span style={{ fontSize: '16px', fontWeight: '600', color: '#52c41a' }}>
            {loading ? (
              <Spin size="small" />
            ) : (
              `金库总额: ${formatUsdAmount(totalVaultValue)}`
            )}
          </span>

        </Space>
      </div>

      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }}>
        {connected && publicKey && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '4px 8px',
            backgroundColor: '#f0f2f5',
            borderRadius: '6px'
          }}>
            <div style={{ fontSize: '16px', fontWeight: '600', lineHeight: 1.6, color: '#52c41a' }}>
              {loading ? (
                <Spin size="small" />
              ) : (
                `${userBalance.toFixed(5)} SOL`
              )}
            </div>
          </div>
        )}
        <WalletMultiButton
          style={{
            height: '36px',
            fontSize: '14px',
            borderRadius: '6px'
          }}
        />
      </div>
    </div>
  );
};

export default GlobalHeaderInfo;
