import React from 'react'
import { Card, Descriptions, Space, message, Alert } from 'antd'
import { useModel } from '@umijs/max'
import AddressCopy from '@/components/AddressCopy'

const Settings: React.FC = () => {
  const {
    currentMultisig: multisig,
    loading,
  } = useModel('multisig')

  const [messageApi, contextHolder] = message.useMessage()

  return (
    <>
      {contextHolder}
      <div>

        {/* 多签信息展示 */}
        {multisig && (
          <Card title="多签信息" loading={loading}>
            <Descriptions bordered>
              <Descriptions.Item label="Members" span={3}>
                <div>{multisig?.members.length}</div>
              </Descriptions.Item>
              <Descriptions.Item label="Threshold" span={3}>
                <Space>{multisig?.threshold}/{multisig?.members?.length ? multisig?.members?.length - 1 : 0}</Space>
              </Descriptions.Item>
              <Descriptions.Item label="Squad Vault" span={3}>
                <AddressCopy address={multisig?.vault.address || ''} showFullAddress={true} />
              </Descriptions.Item>
              <Descriptions.Item label="Multisig Account" span={3}>
                <AddressCopy address={multisig?.multisigAccount || ''} showFullAddress={true} />
              </Descriptions.Item>
            </Descriptions>
          </Card>
        )}
      </div>
    </>
  )
}

export default Settings
