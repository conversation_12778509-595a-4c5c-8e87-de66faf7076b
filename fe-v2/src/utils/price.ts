// 价格计算工具函数

export interface TokenPrice {
  symbol: string;
  price: number; // USD 价格
}

// Token 价格映射（简化版，实际应该从 API 获取）
const TOKEN_PRICES: Record<string, number> = {
  'SOL': 100, // 假设 SOL 价格为 $100
  'USDC': 1,
  'USDT': 1,
  'mSOL': 95,
  'BONK': 0.00001,
  'ETH': 2500,
  'BTC': 45000,
};

/**
 * 获取 SOL 价格（美元）
 * @returns SOL 价格
 */
export async function getSolPrice(): Promise<number> {
  try {
    // 使用 CoinGecko API 获取实时 SOL 价格
    const response = await fetch('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd');
    const data = await response.json();
    return data.solana?.usd || TOKEN_PRICES.SOL;
  } catch (error) {
    console.warn('获取 SOL 价格失败，使用默认价格:', error);
    return TOKEN_PRICES.SOL;
  }
}

/**
 * 获取 Token 价格（美元）
 * @param symbol Token 符号
 * @returns Token 价格
 */
export function getTokenPrice(symbol: string): number {
  return TOKEN_PRICES[symbol] || 0;
}

/**
 * 计算 SOL 的美元价值
 * @param solAmount SOL 数量
 * @param solPrice SOL 价格
 * @returns 美元价值
 */
export function calculateSolValue(solAmount: number, solPrice: number): number {
  return solAmount * solPrice;
}

/**
 * 计算 Token 的美元价值
 * @param tokenAmount Token 数量
 * @param tokenSymbol Token 符号
 * @returns 美元价值
 */
export function calculateTokenValue(tokenAmount: number, tokenSymbol: string): number {
  const price = getTokenPrice(tokenSymbol);
  return tokenAmount * price;
}

/**
 * 格式化美元金额显示
 * @param amount 美元金额
 * @returns 格式化后的字符串
 */
export function formatUsdAmount(amount: number): string {
  if (amount >= 1000000) {
    return `$${(amount / 1000000).toFixed(2)}M`;
  } else if (amount >= 1000) {
    return `$${(amount / 1000).toFixed(2)}K`;
  } else {
    return `$${amount.toFixed(2)}`;
  }
}

/**
 * 计算多签金库总价值（美元）
 * @param multisigs 多签账户列表
 * @param solPrice SOL 价格（可选，后端已计算）
 * @returns 总价值（美元）
 */
export function calculateTotalVaultValue(multisigs: any[], solPrice?: number): number {
  let totalValue = 0;

  multisigs.forEach(multisig => {
    // 使用后端计算好的总价值
    if (multisig.vault.totalValue) {
      totalValue += multisig.vault.totalValue;
    } else {
      // 向后兼容：如果后端没有计算，前端计算
      const solValue = calculateSolValue(multisig.vault.balanceSOL || 0, solPrice || 100);
      totalValue += solValue;

      if (multisig.vault.tokenAccounts) {
        multisig.vault.tokenAccounts.forEach((token: any) => {
          const tokenValue = calculateTokenValue(token.uiAmount || 0, token.name);
          totalValue += tokenValue;
        });
      }
    }
  });

  return totalValue;
}
