// Squads 权限系统工具函数
// 基于 Squads Protocol 的权限位掩码定义

export interface PermissionFlags {
  canPropose: boolean;
  canVote: boolean;
  canExecute: boolean;
}

// Squads 权限位掩码常量
export const PERMISSION_MASKS = {
  PROPOSER: 0x01,  // 0b001 - 可以创建提案
  VOTER: 0x02,     // 0b010 - 可以投票
  EXECUTOR: 0x04,  // 0b100 - 可以执行交易
} as const;

/**
 * 解析权限数组，返回具体权限信息
 * @param permissions 从后端获取的权限字符串数组，如 ["Proposer", "Voter", "Executor"]
 * @returns 解析后的权限标志
 */
export function parsePermissions(permissions: string[]): PermissionFlags {
  // 如果 permissions 是字符串数组
  if (Array.isArray(permissions)) {
    return {
      canPropose: permissions.includes('Proposer'),
      canVote: permissions.includes('Voter'),
      canExecute: permissions.includes('Executor'),
    };
  }

  // 如果 permissions 是对象且有 mask 属性（向后兼容）
  if (permissions && typeof permissions === 'object' && 'mask' in permissions) {
    const mask = permissions.mask;
    return {
      canPropose: (mask & PERMISSION_MASKS.PROPOSER) !== 0,
      canVote: (mask & PERMISSION_MASKS.VOTER) !== 0,
      canExecute: (mask & PERMISSION_MASKS.EXECUTOR) !== 0,
    };
  }

  // 如果 permissions 直接是数字（向后兼容）
  if (typeof permissions === 'number') {
    return {
      canPropose: (permissions & PERMISSION_MASKS.PROPOSER) !== 0,
      canVote: (permissions & PERMISSION_MASKS.VOTER) !== 0,
      canExecute: (permissions & PERMISSION_MASKS.EXECUTOR) !== 0,
    };
  }

  // 默认情况：假设有所有权限（向后兼容）
  return {
    canPropose: true,
    canVote: true,
    canExecute: true,
  };
}

/**
 * 获取权限标签列表
 * @param permissions 权限标志
 * @returns 权限标签数组
 */
export function getPermissionLabels(permissions: PermissionFlags): string[] {
  const labels: string[] = [];

  if (permissions.canPropose) labels.push('Proposer');
  if (permissions.canVote) labels.push('Voter');
  if (permissions.canExecute) labels.push('Executor');

  return labels.length > 0 ? labels : ['无权限'];
}

/**
 * 获取权限的颜色配置
 * @param permission 权限类型
 * @returns 颜色值
 */
export function getPermissionColor(permission: string): string {
  switch (permission) {
    case 'Proposer':
      return '#52c41a'; // 绿色
    case 'Voter':
      return '#1890ff'; // 蓝色
    case 'Executor':
      return '#fa8c16'; // 橙色
    default:
      return '#d9d9d9'; // 灰色
  }
}
