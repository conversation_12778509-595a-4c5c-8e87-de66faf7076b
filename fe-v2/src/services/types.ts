export interface MultisigMember {
  address: string;  // 后端返回的是 address 字段
  permissions: string[];  // 后端返回的是权限字符串数组，如 ["Proposer", "Voter", "Executor"]
}

export interface TokenAccount {
  address: string;
  mint: string;
  name: string;
  balance: number;
  decimals: number;
  uiAmount: number;
  uiAmountString: string;
}

export interface Recipient {
  name: string;
  address: string;
  description: string;
}

export interface Asset {
  symbol: string;
  address: string;
  mint?: string; // Token 的 mint 地址，SOL 没有此字段
  decimals?: number; // Token 的精度，SOL 没有此字段
  balance: number;
  price: number;
  value: number;
  weight: number;
}

export interface VaultInfo {
  address: string;
  balance: number;
  balanceSOL: number;
  assets: Asset[];
  totalValue: number;
}

export interface MultisigInfo {
  multisigAccount: string; // 多签账户地址
  threshold: number;
  transactionIndex: number;
  solPrice: number;
  vault: VaultInfo;
  members: MultisigMember[];
}

export interface Transaction {
  transactionIndex: number;
  status: 'Active' | 'Executed' | 'Rejected' | 'Cancelled' | 'Approved';
  approvals: number;
  threshold: number;
  votes: Array<{
    member: string;
    vote: 'Approve' | 'Reject';
  }>;
  canExecute: boolean;
  // 转账信息字段
  transactionType: string;
  transferToken: string | null;
  from: string | null; // 转账来源地址
  to: string | null; // 转账目标地址
  amount: number | null; // 转账金额
  createdAt: string;
}

// 分页信息
export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 交易列表响应
export interface TransactionListResponse {
  transactions: Transaction[];
  pagination: Pagination;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}