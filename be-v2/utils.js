import axios from 'axios'
import * as multisig from '@sqds/multisig'
import { Connection, PublicKey, SystemProgram } from '@solana/web3.js'
import TOKENS from '../tokens.json' assert {type:'json'}
import { MULTISIG_PUBKEY, SQUADS_V4_PUBKEY, SPL_TOKEN_PUBKEY, SOLANA_RPC_URL, TX_TIMEOUT, TX_MAX_RETRIES, PERMISSION_MASKS } from './constants.js'

export const connection = new Connection(SOLANA_RPC_URL, 'confirmed')

export const handleError = (ctx, error) => {
  console.error(`error: ${error}`)
  ctx.status = 200
  ctx.body = { error }
}

export async function getPrice(chain) {
  // TODO 1: 假的
  let price = 0
  const tokenPrices = {
    'USDC': 1,
    'USDT': 1,
    'SOL': 100,
    'mSOL': 95,
    'BONK': 0.00001,
    'ETH': 2500,
    'BTC': 45000,
    'solusdc': 1,
    'solusdt': 1,
  }
  try {
    if (chain === 'SOL') {
      const { data } = await axios.get('https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd', { timeout: 5000 })
      price = data?.solana?.usd || 0
    } else {
      price = tokenPrices[chain] || 0
    }
  } catch (error) {
    console.error(`get ${chain} price error: ${error.message}`)
  }
  return price
}

export async function getTokenAccounts(vaultPda) {
  const tokenAccounts = []
  try {
    const tokenAccountsResponse = await connection.getParsedTokenAccountsByOwner(vaultPda, { programId: SPL_TOKEN_PUBKEY})
    for (const { pubkey, account: { data: { parsed: { info: { mint, tokenAmount: { uiAmount, amount, decimals, uiAmountString }}}}}} of tokenAccountsResponse.value) {
      // 只包含有余额的 Token 账户
      if (uiAmount > 0) {
        const tokenPrice = await getPrice(getTokenName(mint))
        tokenAccounts.push({
          address: pubkey.toBase58(),
          mint: mint,
          name: getTokenName(mint),
          balance: parseInt(amount),
          decimals: decimals,
          uiAmount: uiAmount,
          uiAmountString: uiAmountString,
          price: tokenPrice,
          value: uiAmount * tokenPrice
        })
      }
    }
  } catch (error) {
    console.error(`get token account error:`, error.message)
  }
  return tokenAccounts
}
// ==============

// 工具函数
export const validateParams = (params, required) => {
  const missing = required.filter(key => !params[key])
  return missing.length ? `缺少参数: ${missing.join(', ')}` : null
}

export const sendTransaction = async (signedTransaction, options = {}) => {
  const signature = await connection.sendRawTransaction(
    Buffer.from(signedTransaction, 'base64'),
    {
      skipPreflight: true,
      preflightCommitment: 'confirmed',
      maxRetries: TX_MAX_RETRIES,
      ...options
    }
  )
  return signature
}

export const confirmTransactionWithTimeout = async (signature, timeout = TX_TIMEOUT) => {
  try {
    const result = await Promise.race([
      connection.confirmTransaction(signature, 'confirmed'),
      new Promise((_, reject) => setTimeout(() => reject(new Error('确认超时')), timeout))
    ])
    return { confirmed: true, result }
  } catch (error) {
    return { confirmed: false, error: error.message }
  }
}

// ==================== 交易解析功能 ====================

// /**
//  * 解析指令数据格式（统一处理对象或Buffer）
//  */
// export function parseInstructionData(instruction) {
//   console.log('Buffer.isBuffer(instruction.data): ', Buffer.isBuffer(instruction.data))
//   console.log('typeof instruction.data === ', typeof instruction.data === 'object')
//   if (Buffer.isBuffer(instruction.data)) {
//     return instruction.data
//   } else if (typeof instruction.data === 'object') {
//     return Buffer.from(Object.values(instruction.data))
//   }

//   return null
// }

// /**
//  * 解析账户索引（统一处理数组或对象）
//  */
// export function parseAccountIndexes(instruction) {
//   console.log('instruction.accountKeyIndexes: ', instruction.accountKeyIndexes)
//   console.log('instruction.accountIndexes: ', instruction.accountIndexes)
//   console.log('Array.isArray(instruction.accountIndexes): ', Array.isArray(instruction.accountIndexes))


//   if (instruction.accountKeyIndexes) {
//     return instruction.accountKeyIndexes
//   } else if (instruction.accountIndexes) {
//     return Array.isArray(instruction.accountIndexes)
//       ? instruction.accountIndexes
//       : Object.values(instruction.accountIndexes)
//   }
//   return []
// }

export async function parseSystemTransfer(instruction, message) {
  try {
    const dataBuffer = Buffer.from(Object.values(instruction.data))
    const accountIndexes = Object.values(instruction.accountIndexes)
    return {
      fromAddress: message.accountKeys[accountIndexes[0]].toBase58(),
      toAddress: message.accountKeys[accountIndexes[1]].toBase58(),
      amount: Number(dataBuffer.readBigUInt64LE(4)) / 1e9,
      type: 'sol',
      tokenSymbol: 'SOL',
      decimals: 9
    }
  } catch (error) {
    return null
  }
}

// export async function parseSystemTransferFallback(instruction, message) {
//   try {
//     const dataBuffer = Buffer.from(Object.values(instruction.data)) // parseInstructionData(instruction)

//     // const instructionType = dataBuffer.readUInt32LE(0)
//     // if (instructionType !== 2) return null

//     // const amount =
//     const accountIndexes = Object.values(instruction.accountIndexes) // parseAccountIndexes(instruction)

//     // if (accountIndexes.length < 2) return null

//     // const fromIndex =
//     // const toIndex =

//     // 确保账户索引在范围内
//     // if (fromIndex >= message.accountKeys.length || toIndex >= message.accountKeys.length) {
//     //   return null
//     // }

//     // const fromAddress =
//     // const toAddress =

//     return {
//       type: 'sol',
//       amount: Number(dataBuffer.readBigUInt64LE(4)) / 1e9,
//       fromAddress: message.accountKeys[accountIndexes[0]].toBase58(),
//       toAddress: message.accountKeys[accountIndexes[1]].toBase58(),
//       tokenSymbol: 'SOL',
//       decimals: 9
//     }
//   } catch (error) {
//     return null
//   }
// }

// export async function parseTransactionDetails(transactionIndex) {
//   try {
//     // 获取 Proposal 数据
//     const [proposalPda] = multisig.getProposalPda({
//       multisigPda: MULTISIG_PUBKEY,
//       transactionIndex: BigInt(transactionIndex),
//       programId: SQUADS_V4_PUBKEY
//     })
//     const proposalAccount = await connection.getAccountInfo(proposalPda)
//     const proposalData = multisig.accounts.Proposal.fromAccountInfo(proposalAccount)[0]

//     console.log('=== 3 ', {
//       multisigPda: MULTISIG_PUBKEY,
//       index: BigInt(transactionIndex),
//       programId: SQUADS_V4_PUBKEY
//     })
//     // 获取 VaultTransaction 数据
//     const [transactionPda] = multisig.getTransactionPda({
//       multisigPda: MULTISIG_PUBKEY,
//       index: BigInt(transactionIndex),
//       programId: SQUADS_V4_PUBKEY
//     })

//     const transactionAccount = await connection.getAccountInfo(transactionPda)
//     // console.log('=== 3 transactionAccount: ', transactionAccount)
//     if (!transactionAccount) return null

//     const vaultTransactionData = multisig.accounts.VaultTransaction.fromAccountInfo(transactionAccount)[0]
//     const message = vaultTransactionData.message

//     // 初始化转账信息
//     const transferInfo = {
//       type: 'unknown',
//       amount: null,
//       toAddress: null,
//       fromAddress: null,
//       tokenMint: null,
//       tokenSymbol: null,
//       decimals: null,
//       createdAt: null,
//       creator: proposalData.creator?.toBase58() || 'Unknown'
//     }

//     // 解析每个指令
//     for (const instruction of message.instructions) {
//       // 检查 programIdIndex 是否超出范围（官方平台兼容性处理）
//       if (instruction.programIdIndex >= message.accountKeys.length) {
//         const solTransfer = await parseSystemTransfer(instruction, message)
//         if (solTransfer) {
//           Object.assign(transferInfo, solTransfer)
//           break
//         }
//         console.log('==== continue - v1')
//         continue
//       }
//       console.log('==== continue - v2')

//       const programId = message.accountKeys[instruction.programIdIndex]

//       // 解析 SOL 转账
//       if (programId.equals(SystemProgram.programId)) {
//         const solTransfer = await parseSystemTransfer(instruction, message)
//         if (solTransfer) {
//           Object.assign(transferInfo, solTransfer)
//           break
//         }
//       }
//       // 可以在这里添加 SPL Token 解析，但目前项目中未使用
//     }

//     // 获取创建时间
//     if (proposalData.status && proposalData.status.timestamp) {
//       const timestamp = Number(proposalData.status.timestamp.toString())
//       transferInfo.createdAt = new Date(timestamp * 1000).toISOString()
//     }

//     return transferInfo

//   } catch (error) {
//     console.error(`解析交易 ${transactionIndex} 失败:`, error.message)
//     return null
//   }
// }

// export async function enhanceTransactionList(transactions) {
//   const enhanced = []

//   for (const tx of transactions) {
//     const parsedDetails = await parseTransactionDetails(tx.transactionIndex)
//     enhanced.push({
//       ...tx,
//       transactionType: parsedDetails.type,
//       transferAmount: parsedDetails.amount,
//       transferToken: parsedDetails.tokenSymbol,
//       toAddress: parsedDetails.toAddress,
//       fromAddress: parsedDetails.fromAddress,
//       tokenMint: parsedDetails.tokenMint,
//       createdAt: parsedDetails.createdAt || tx.createdAt,
//       creator: parsedDetails.creator || tx.creator,
//       isParsed: true,
//       parseSource: 'vaultTransaction'
//     })
//   }

//   return enhanced
// }

const TOKEN_NAME_MAP = {}
for (const [symbol, { asset_id }] of Object.entries(TOKENS)) {
  TOKEN_NAME_MAP[asset_id] = symbol
}

export const getTokenName = mint => TOKEN_NAME_MAP[mint]



export function parsePermissions(mask) {
  const permissions = []
  if ((mask & PERMISSION_MASKS.PROPOSER) !== 0) permissions.push('Proposer')
  if ((mask & PERMISSION_MASKS.VOTER) !== 0) permissions.push('Voter')
  if ((mask & PERMISSION_MASKS.EXECUTOR) !== 0) permissions.push('Executor')
  return permissions
}


/**
 * 计算资产权重占比
 */
export function calculateAssetWeights(assets) {
  const totalValue = assets.reduce((sum, asset) => sum + asset.value, 0)

  return assets.map(asset => ({
    ...asset,
    weight: totalValue > 0 ? asset.value / totalValue : 0
  }))
}
