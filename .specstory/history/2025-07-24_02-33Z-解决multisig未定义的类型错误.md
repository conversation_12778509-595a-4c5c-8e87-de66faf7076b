<!-- Generated by SpecStory -->

# 解决multisig未定义的类型错误 (2025-07-24 02:33Z)

_**User**_

选中行提示：“multisig”可能为“未定义”。ts(18048)

---

_**Assistant**_

这是因为multisig的初始值是undefined，数据还没加载时就会渲染到这行。  
你需要在渲染前加判断，确保multisig有值再渲染Card组件。  
例如：

```tsx
{multisig && (
  <Card key={multisig.address} style={{ marginBottom: 24 }} loading={loading}>
    {/* ...原有内容... */}
  </Card>
)}
```

这样可以避免“multisig可能为未定义”的类型报错。

---

