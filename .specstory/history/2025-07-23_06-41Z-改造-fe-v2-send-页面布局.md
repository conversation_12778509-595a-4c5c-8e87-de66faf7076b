<!-- Generated by SpecStory -->

# 改造 fe-v2 Send 页面布局 (2025-07-23 06:41Z)

_**User**_

请改造 fe-v2 目录下的前端代码：
1.更新 Send 页面表单布局和结构，把 sol 转账和 Token 转账合并，因为两个差异就一个 token选项，在发送交易时，判断是否有 token 来调用不同的接口即可
2.页面组件最好用现成的 ant-design 

---

_**Assistant**_

我会帮你改造 Send 页面。首先让我查看相关代码。

---

<details>
            <summary>Searched codebase "Where is the Send page component located in fe-v2?" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `fe-v2/src/pages/Send/index.tsx` | L1-40 |
| `fe-v2/.umirc.ts` | L1-54 |
| `fe-v2/src/pages/Send/index.tsx` | L40-42 |
| `fe-v2/src/pages/Transactions/index.tsx` | L1-26 |
| `fe-v2/src/components/SolTransfer.tsx` | L1-12 |
| `fe-v2/src/pages/Members/index.tsx` | L1-22 |
| `fe-v2/src/components/SolTransfer.tsx` | L12-57 |
| `fe-v2/src/components/TokenTransfer.tsx` | L1-13 |
| `fe-v2/src/pages/Settings/index.tsx` | L1-64 |
| `fe-v2/src/components/TokenTransfer.tsx` | L13-77 |
| `fe-v2/src/app.tsx` | L12-42 |
| `fe-v2/src/components/SolTransfer.tsx` | L241-313 |
| `fe-v2/src/pages/Transactions/index.tsx` | L26-28 |
| `fe-v2/src/components/Guide/index.ts` | L1-2 |
| `fe-v2/src/pages/Settings/index.tsx` | L148-150 |
| `fe-v2/package.json` | L1-40 |
| `fe-v2/src/pages/Account/index.tsx` | L177-179 |
| `fe-v2/src/pages/Account/index.tsx` | L46-110 |
| `fe-v2/src/components/ProposalManagement.tsx` | L1-12 |
| `fe-v2/src/pages/Account/index.tsx` | L1-15 |
| `fe-v2/src/components/Guide/Guide.tsx` | L1-7 |
| `fe-v2/src/components/Guide/Guide.tsx` | L7-21 |
| `fe-v2/src/app.tsx` | L1-12 |
| `fe-v2/src/services/migration-guide.md` | L1-54 |
| `fe-v2/src/components/SolTransfer.tsx` | L345-347 |

</details>

---

让我们继续查看 TokenTransfer 组件的完整内容。

---

Read file: fe-v2/src/components/TokenTransfer.tsx

---

根据代码分析，我将帮你合并 SOL 和 Token 转账功能，创建一个新的统一转账组件。主要改动如下：

1. 创建新的 `Transfer` 组件，合并 SOL 和 Token 转账功能
2. 使用 antd 组件库重新设计表单布局
3. 根据是否选择 token 来决定调用不同的转账接口

让我们开始改造：

---

```diff
- 
+ import React, { useState, useCallback } from 'react';
+ import { useWallet, useConnection } from '@solana/wallet-adapter-react';
+ import { PublicKey, SystemProgram, TransactionMessage, VersionedTransaction, LAMPORTS_PER_SOL } from '@solana/web3.js';
+ import { TOKEN_PROGRAM_ID, getAssociatedTokenAddress, createTransferInstruction } from '@solana/spl-token';
+ import * as multisig from '@sqds/multisig';
+ import { Form, Input, Button, Select, Card, Space, Typography, message } from 'antd';
+ import { SendOutlined } from '@ant-design/icons';
+ import MultisigSelector from './MultisigSelector';
+ import { apiService } from '@/services/ApiService';
+ 
+ const { Text } = Typography;
+ const { Option } = Select;
+ 
+ interface TransferProps {
+   onSuccess?: (signature: string) => void;
+   onError?: (error: string) => void;
+ }
+ 
+ // 常用Token列表（主网）
+ const commonTokens = [
+   { name: 'USDC', mint: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', decimals: 6 },
+ ];
+ 
+ const Transfer: React.FC<TransferProps> = ({ onSuccess, onError }) => {
+   const { publicKey, signTransaction } = useWallet();
+   const { connection } = useConnection();
+   const [form] = Form.useForm();
+   const [loading, setLoading] = useState(false);
+   const [selectedMultisig, setSelectedMultisig] = useState('');
+ 
+   // 默认配置
+   const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");
+ 
+   const handleCreateNew = () => {
+     onError?.('创建新多签账户功能将在后续步骤中实现');
+   };
+ 
+   const validateInputs = (values: any) => {
+     if (!publicKey) {
+       throw new Error('请先连接钱包');
+     }
+     if (!selectedMultisig) {
+       throw new Error('请选择多签账户');
+     }
+     if (!values.recipient) {
+       throw new Error('请输入接收地址');
+     }
+     if (!values.amount || parseFloat(values.amount) <= 0) {
+       throw new Error('请输入有效的转账金额');
+     }
+ 
+     // 验证地址格式
+     try {
+       new PublicKey(selectedMultisig);
+     } catch {
+       throw new Error('多签地址格式不正确');
+     }
+ 
+     try {
+       new PublicKey(values.recipient);
+     } catch {
+       throw new Error('接收地址格式不正确');
+     }
+ 
+     if (values.tokenMint) {
+       try {
+         new PublicKey(values.tokenMint);
+       } catch {
+         throw new Error('Token合约地址格式不正确');
+       }
+     }
+   };
+ 
+   const handleTransfer = useCallback(async (values: any) => {
+     if (!signTransaction) {
+       onError?.('钱包不支持签名功能');
+       return;
+     }
+ 
+     try {
+       setLoading(true);
+       validateInputs(values);
+ 
+       const multisigPda = new PublicKey(selectedMultisig);
+       const recipientPubkey = new PublicKey(values.recipient);
+ 
+       // 获取多签和金库信息
+       const vaultInfo = await apiService.getVaultInfo(selectedMultisig);
+       const { multisigInfo, vaultInfo: vault } = vaultInfo;
+ 
+       // 检查当前用户是否是多签成员
+       const isMember = multisigInfo.members.some(member => member.key === publicKey!.toBase58());
+       if (!isMember) {
+         throw new Error('当前钱包不是多签成员');
+       }
+ 
+       const vaultPda = new PublicKey(vault.address);
+       let transferInstruction;
+       const transferAmount = values.tokenMint
+         ? Math.floor(parseFloat(values.amount) * Math.pow(10, values.decimals || 6))
+         : Math.floor(parseFloat(values.amount) * LAMPORTS_PER_SOL);
+ 
+       if (values.tokenMint) {
+         // Token 转账
+         const tokenMintPubkey = new PublicKey(values.tokenMint);
+         const vaultTokenAccount = await getAssociatedTokenAddress(
+           tokenMintPubkey,
+           vaultPda,
+           true
+         );
+         const recipientTokenAccount = await getAssociatedTokenAddress(
+           tokenMintPubkey,
+           recipientPubkey
+         );
+ 
+         // 检查金库Token账户余额
+         const vaultTokenBalance = await apiService.getTokenBalance(vaultTokenAccount.toBase58());
+         if (vaultTokenBalance.balance < transferAmount) {
+           throw new Error(`Token余额不足。当前余额: ${(vaultTokenBalance.balance / Math.pow(10, values.decimals || 6)).toFixed(values.decimals || 6)} Token`);
+         }
+ 
+         transferInstruction = createTransferInstruction(
+           vaultTokenAccount,
+           recipientTokenAccount,
+           vaultPda,
+           transferAmount,
+           [],
+           TOKEN_PROGRAM_ID
+         );
+       } else {
+         // SOL 转账
+         const vaultBalance = await connection.getBalance(vaultPda);
+         if (vaultBalance < transferAmount) {
+           throw new Error(`SOL余额不足。当前余额: ${vaultBalance / LAMPORTS_PER_SOL} SOL`);
+         }
+ 
+         transferInstruction = SystemProgram.transfer({
+           fromPubkey: vaultPda,
+           toPubkey: recipientPubkey,
+           lamports: transferAmount,
+         });
+       }
+ 
+       const { blockhash } = await apiService.getLatestBlockhash();
+       const transferMessage = new TransactionMessage({
+         payerKey: vaultPda,
+         recentBlockhash: blockhash,
+         instructions: [transferInstruction],
+       });
+ 
+       const nextTransactionIndex = BigInt(multisigInfo.transactionIndex) + 1n;
+ 
+       // 创建交易并投票
+       const createIx = multisig.instructions.vaultTransactionCreate({
+         multisigPda,
+         transactionIndex: nextTransactionIndex,
+         creator: publicKey!,
+         vaultIndex: 0,
+         ephemeralSigners: 0,
+         transactionMessage: transferMessage,
+         memo: `Transfer ${values.amount} ${values.tokenMint ? 'Token' : 'SOL'} to ${values.recipient.substring(0, 8)}...`,
+         programId,
+       });
+ 
+       const proposalIx = multisig.instructions.proposalCreate({
+         multisigPda,
+         transactionIndex: nextTransactionIndex,
+         creator: publicKey!,
+         isDraft: false,
+         programId,
+       });
+ 
+       const approveIx = multisig.instructions.proposalApprove({
+         multisigPda,
+         transactionIndex: nextTransactionIndex,
+         member: publicKey!,
+         programId,
+       });
+ 
+       const combinedMessage = new TransactionMessage({
+         payerKey: publicKey!,
+         recentBlockhash: blockhash,
+         instructions: [createIx, proposalIx, approveIx],
+       });
+ 
+       const combinedTx = new VersionedTransaction(combinedMessage.compileToV0Message());
+       const signedTx = await signTransaction(combinedTx);
+ 
+       // 发送交易
+       const result = values.tokenMint
+         ? await apiService.createTokenTransfer({
+             multisigAddress: selectedMultisig,
+             recipientAddress: values.recipient,
+             tokenMint: values.tokenMint,
+             amount: values.amount,
+             decimals: values.decimals || 6,
+             creatorPublicKey: publicKey!.toBase58(),
+             signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
+           })
+         : await apiService.createSolTransfer({
+             multisigAddress: selectedMultisig,
+             recipientAddress: values.recipient,
+             amount: values.amount,
+             creatorPublicKey: publicKey!.toBase58(),
+             signedTransaction: Buffer.from(signedTx.serialize()).toString('base64')
+           });
+ 
+       message.success('转账交易创建成功！等待其他成员投票...');
+       onSuccess?.(result.signature);
+ 
+     } catch (error: any) {
+       console.error('转账失败:', error);
+       onError?.(error.message || '转账失败');
+     } finally {
+       setLoading(false);
+     }
+   }, [publicKey, signTransaction, selectedMultisig, connection, onSuccess, onError]);
+ 
+   const handleTokenSelect = (token: { name: string; mint: string; decimals: number }) => {
+     form.setFieldsValue({
+       tokenMint: token.mint,
+       decimals: token.decimals,
+     });
+   };
+ 
+   return (
+     <Card title="创建转账交易">
+       <Form
+         form={form}
+         layout="vertical"
+         onFinish={handleTransfer}
+         initialValues={{
+           amount: '0.0001',
+           decimals: 6,
+         }}
+       >
+         <MultisigSelector
+           selectedMultisig={selectedMultisig}
+           onSelect={setSelectedMultisig}
+           onCreateNew={handleCreateNew}
+         />
+ 
+         <Form.Item
+           label="Token类型"
+           extra="如不选择则默认转账 SOL"
+         >
+           <Space direction="vertical" style={{ width: '100%' }}>
+             <Space wrap>
+               {commonTokens.map((token) => (
+                 <Button
+                   key={token.mint}
+                   onClick={() => handleTokenSelect(token)}
+                   type={form.getFieldValue('tokenMint') === token.mint ? 'primary' : 'default'}
+                   size="small"
+                 >
+                   {token.name}
+                 </Button>
+               ))}
+             </Space>
+             <Form.Item
+               name="tokenMint"
+               noStyle
+             >
+               <Input placeholder="或输入Token合约地址" />
+             </Form.Item>
+           </Space>
+         </Form.Item>
+ 
+         <Form.Item
+           name="decimals"
+           label="Token小数位数"
+           hidden={!form.getFieldValue('tokenMint')}
+         >
+           <Input type="number" min={0} max={18} />
+         </Form.Item>
+ 
+         <Form.Item
+           name="recipient"
+           label="接收地址"
+           rules={[{ required: true, message: '请输入接收地址' }]}
+         >
+           <Input placeholder="输入接收地址" />
+         </Form.Item>
+ 
+         <Form.Item
+           name="amount"
+           label="转账金额"
+           rules={[{ required: true, message: '请输入转账金额' }]}
+         >
+           <Input type="number" min="0.0001" step="0.0001" placeholder="输入转账金额" />
+         </Form.Item>
+ 
+         <Form.Item>
+           <Button
+             type="primary"
+             htmlType="submit"
+             loading={loading}
+             disabled={!publicKey || !selectedMultisig}
+             icon={<SendOutlined />}
+             block
+           >
+             创建转账交易
+           </Button>
+         </Form.Item>
+       </Form>
+ 
+       <Card size="small" title="说明" style={{ marginTop: 16 }}>
+         <ul style={{ paddingLeft: 20, margin: 0 }}>
+           <li>支持 SOL 和所有 SPL Token 转账</li>
+           <li>需要确保金库有足够的余额</li>
+           <li>Token 转账时，接收地址必须有对应的 Token 账户</li>
+           <li>请确认 Token 合约地址和小数位数正确</li>
+           <li>建议先用小额测试</li>
+         </ul>
+       </Card>
+     </Card>
+   );
+ };
+ 
+ export default Transfer;
```

---

现在让我们更新 Send 页面来使用新的 Transfer 组件：

---

```diff
  import React from 'react';
- import { Card, Tabs, message } from 'antd';
- import SolTransfer from '@/components/SolTransfer';
- import TokenTransfer from '@/components/TokenTransfer';
+ import { message } from 'antd';
+ import Transfer from '@/components/Transfer';
  
  const SendPage: React.FC = () => {
```

```diff
      messageApi.error(text);
    };
- 
-   const items = [
-     {
-       key: 'sol',
-       label: 'SOL 转账',
-       children: <SolTransfer onSuccess={handleSuccess} onError={handleError} />,
-     },
-     {
-       key: 'token',
-       label: 'Token 转账',
-       children: <TokenTransfer onSuccess={handleSuccess} onError={handleError} />,
-     },
-   ];
  
    return (
      <>
        {contextHolder}
        <div style={{ padding: '24px' }}>
-         <Card>
-           <Tabs items={items} />
-         </Card>
+         <Transfer onSuccess={handleSuccess} onError={handleError} />
        </div>
      </>
```

---

现在我们已经完成了代码改造：

1. 创建了新的 `Transfer` 组件，合并了 SOL 和 Token 转账功能：
   - 使用 antd 的 Form、Card、Button 等组件重新设计了表单布局
   - 根据是否选择 token 来决定调用不同的转账接口
   - 保留了原有的多签验证和交易创建逻辑
   - 添加了更友好的错误提示和状态反馈

2. 更新了 Send 页面：
   - 移除了原有的 Tabs 组件
   - 使用新的 Transfer 组件替换了分开的 SOL 和 Token 转账组件
   - 保持了原有的消息提示功能

主要改进：
1. 界面更简洁：合并了转账功能，减少了用户操作步骤
2. 使用 antd 组件：提供了更好的用户体验和视觉效果
3. 代码更简洁：减少了重复代码，逻辑更清晰
4. 错误处理更完善：添加了更多的错误检查和友好提示

你可以删除原来的 `SolTransfer.tsx` 和 `TokenTransfer.tsx` 文件，因为它们已经不再使用了。需要我帮你删除这些文件吗？

---

